<!--- DO NOT EDIT THIS FILE. Generated by README.py-->
# Inferring velocity from tracer

```
./veltracer.py
```
Output directory `out_veltracer`:
* [`train.log`](https://cselab.github.io/odil/examples/out_veltracer/train.log)
* [`train.csv`](https://cselab.github.io/odil/examples/out_veltracer/train.csv)
* [`u_00005.png`](https://cselab.github.io/odil/examples/out_veltracer/u_00005.png)  
  <img src="https://cselab.github.io/odil/examples/out_veltracer/u_00005.png" width=300>
* [`vx_00005.png`](https://cselab.github.io/odil/examples/out_veltracer/vx_00005.png)  
  <img src="https://cselab.github.io/odil/examples/out_veltracer/vx_00005.png" width=300>
