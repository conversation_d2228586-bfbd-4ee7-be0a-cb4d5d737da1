#!/usr/bin/env python3

import os

text = """<!--- DO NOT EDIT THIS FILE. Generated by [[GEN]]-->
# Advection-diffusion with parameters

```
./infer_constant.py
```
"""

outdir = "out_infer_constant"
text += "Output directory `[[OUTDIR]]`:\n"
text += "* [`train.log`]([[EXAMPLES]]/[[OUTDIR]]/train.log)\n"
text += "* [`train.csv`]([[EXAMPLES]]/[[OUTDIR]]/train.csv)\n"

for i in range(4):
    png = f"u_0000{i}.png"
    text += f"* [`{png}`]([[EXAMPLES]]/[[OUTDIR]]/{png})  \n"
    text += f'  <img src="[[EXAMPLES]]/[[OUTDIR]]/{png}" height=200>\n'

gen = text
gen = gen.replace("[[GEN]]", os.path.basename(__file__))
gen = gen.replace("[[EXAMPLES]]", "https://cselab.github.io/odil/examples")
gen = gen.replace("[[OUTDIR]]", outdir)
with open("README.md", "w") as f:
    f.write(gen)
