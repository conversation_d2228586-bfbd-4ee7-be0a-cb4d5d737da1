<!--- DO NOT EDIT THIS FILE. Generated by README.py-->
# Advection-diffusion with parameters

```
./infer_constant.py
```
Output directory `out_infer_constant`:
* [`train.log`](https://cselab.github.io/odil/examples/out_infer_constant/train.log)
* [`train.csv`](https://cselab.github.io/odil/examples/out_infer_constant/train.csv)
* [`u_00000.png`](https://cselab.github.io/odil/examples/out_infer_constant/u_00000.png)  
  <img src="https://cselab.github.io/odil/examples/out_infer_constant/u_00000.png" height=200>
* [`u_00001.png`](https://cselab.github.io/odil/examples/out_infer_constant/u_00001.png)  
  <img src="https://cselab.github.io/odil/examples/out_infer_constant/u_00001.png" height=200>
* [`u_00002.png`](https://cselab.github.io/odil/examples/out_infer_constant/u_00002.png)  
  <img src="https://cselab.github.io/odil/examples/out_infer_constant/u_00002.png" height=200>
* [`u_00003.png`](https://cselab.github.io/odil/examples/out_infer_constant/u_00003.png)  
  <img src="https://cselab.github.io/odil/examples/out_infer_constant/u_00003.png" height=200>
