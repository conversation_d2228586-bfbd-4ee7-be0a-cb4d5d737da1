[project]
name = "odil"
version = "0.1.8"
authors = [
    { name="<PERSON><PERSON>", email="p<PERSON><PERSON><PERSON>@gmail.com" },
    { name="<PERSON>", email="<EMAIL>" },
]
description = "ODIL (Optimizing a DIscrete Loss) is a framework for solving inverse problems for differential equations"
readme = "README.md"
dependencies = [
    "numpy",
    "psutil",
    "scipy",
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.urls]
Homepage = "https://github.com/cselab/odil"
Issues = "https://github.com/cselab/odil/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 120
