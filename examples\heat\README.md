<!--- DO NOT EDIT THIS FILE. Generated by README.py-->
# Inferring conductivity from temperature


### Reference solution

```
case=0 outdir=out_ref ./run
```

Output directory `out_ref`:

* [`train.log`](https://cselab.github.io/odil/examples/heat/out_ref/train.log)

### ODIL Newton

```
case=2n outdir=out_odiln ./run
```

Output directory `out_odiln`:

* [`train.log`](https://cselab.github.io/odil/examples/heat/out_odiln/train.log)

* [`u_00010.png`](https://cselab.github.io/odil/examples/heat/out_odiln/u_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_odiln/u_00010.png" height=200>
* [`k_00010.png`](https://cselab.github.io/odil/examples/heat/out_odiln/k_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_odiln/k_00010.png" height=150>


### ODIL Adam

```
case=2 outdir=out_odil ./run
```

Output directory `out_odil`:

* [`train.log`](https://cselab.github.io/odil/examples/heat/out_odil/train.log)

* [`u_00010.png`](https://cselab.github.io/odil/examples/heat/out_odil/u_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_odil/u_00010.png" height=200>
* [`k_00010.png`](https://cselab.github.io/odil/examples/heat/out_odil/k_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_odil/k_00010.png" height=150>


### PINN Adam

```
case=2p gpus=0 outdir=out_pinn ./run
```

Output directory `out_pinn`:

* [`train.log`](https://cselab.github.io/odil/examples/heat/out_pinn/train.log)

* [`u_00010.png`](https://cselab.github.io/odil/examples/heat/out_pinn/u_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_pinn/u_00010.png" height=200>
* [`k_00010.png`](https://cselab.github.io/odil/examples/heat/out_pinn/k_00010.png)  
  <img src="https://cselab.github.io/odil/examples/heat/out_pinn/k_00010.png" height=150>

### Training history

```
./plot_train.py
```

* [`heat_train_u.png`](https://cselab.github.io/odil/examples/heat/heat_train_u.png)  
  <img src="https://cselab.github.io/odil/examples/heat/heat_train_u.png" height=200>
* [`heat_train_k.png`](https://cselab.github.io/odil/examples/heat/heat_train_k.png)  
  <img src="https://cselab.github.io/odil/examples/heat/heat_train_k.png" height=200>
